import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:subfeeds/app/core/scale/dimensionsAutoScaleResolver.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:subfeeds/app/presentation/widgets/subfeeds_loading.dart';
import 'package:subfeeds/app/presentation/screens/feeds/widgets/share_bottom_sheet.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';

class ArticleItem extends StatefulWidget {
  final Map<String, dynamic> article;
  final Map<String, dynamic> feed;
  final bool isDarkMode;
  final Function() onTap;
  final Function() onReadLater;
  final Function() onBookmark;
  final Function()? onShare;
  final bool isShowLaterRead;
  final bool isShowBookmark;
  const ArticleItem({
    super.key,
    required this.article,
    required this.feed,
    required this.isDarkMode,
    required this.onTap,
    required this.onReadLater,
    required this.onBookmark,
    this.onShare,
    this.isShowLaterRead = true,
    this.isShowBookmark = true,
  });

  @override
  State<ArticleItem> createState() => _ArticleItemState();
}

/// 格式化时间
String formatTime(dynamic timestamp) {
  if (timestamp == null) return '';

  try {
    final now = DateTime.now();
    DateTime dateTime;

    if (timestamp is int) {
      dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    } else if (timestamp is String) {
      dateTime = DateTime.parse(timestamp);
    } else {
      return timestamp.toString();
    }

    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}' + 'days_ago'.tr;
    } else if (difference.inHours > 0) {
      return '${difference.inHours}' + 'hours_ago'.tr;
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}' + 'minutes_ago'.tr;
    } else {
      return 'just_now'.tr;
    }
  } catch (e) {
    return timestamp.toString();
  }
}

class _ArticleItemState extends State<ArticleItem> {
  bool _imageLoadFailed = false;

  @override
  Widget build(BuildContext context) {
    final isRead = widget.article['isRead'] == 1;
    final backgroundColor = isRead
        ? (widget.isDarkMode
            ? const Color(0xFF3e3e3e)
            : const Color(0xFFdcdcdc))
        : widget.isDarkMode
            ? const Color(0xFF565656)
            : const Color(0xFFffffff);
    final borderColor =
        widget.isDarkMode ? const Color(0xFF747474) : const Color(0xFFD5D5D5);
    final isPadding = widget.isShowLaterRead || widget.isShowBookmark;

    return Container(
      clipBehavior: Clip.hardEdge,
      padding: isPadding
          ? EdgeInsets.only(left: 10.spx, right: 10.spx, top: 10.spx)
          : EdgeInsets.all(10.spx),
      margin: EdgeInsets.only(bottom: 4.spx),
      decoration: BoxDecoration(
        color: backgroundColor,
        border: Border.all(color: borderColor, width: 1),
        borderRadius: BorderRadius.circular(10.spx),
      ),
      child: InkWell(
        onTap: widget.onTap,
        child: Padding(
          padding: EdgeInsets.zero,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 第一行：标题和封面图片
              _buildFirstRow(context),
              if (!isPadding) SizedBox(height: 10.spx),
              // 第二行：源信息、日期和操作按钮
              _buildSecondRow(context),
            ],
          ),
        ),
      ),
    );
  }

  /// 处理分享功能
  void _handleShare(BuildContext context) {
    if (widget.onShare != null) {
      widget.onShare!();
    } else {
      // 使用默认的分享功能
      final shareData = {
        'name': widget.article['title'] ?? 'Article',
        'link': widget.article['link'] ?? '',
        'description': widget.article['description'] ?? '',
      };
      ShareBottomSheet.show(context, shareData);
    }
  }

  /// 构建第一行：标题和封面图片
  Widget _buildFirstRow(BuildContext context) {
    // 检查是否有有效的封面图片，同时考虑加载失败状态
    final hasValidImage = widget.article['img'] != null &&
        widget.article['img'].toString().isNotEmpty &&
        !_imageLoadFailed;

    return Slidable(
      key: Key(
          'article_first_row_${widget.article['id'] ?? DateTime.now().millisecondsSinceEpoch}'),
      endActionPane: ActionPane(
        motion: const ScrollMotion(),
        extentRatio: 0.55, // 控制滑动面板的宽度
        children: [
          // 稍后阅读
          SizedBox(width: 4.spx),
          CustomSlidableAction(
            onPressed: (context) => widget.onReadLater(),
            backgroundColor: const Color(0xFF4d67ff),
            padding: EdgeInsets.zero,
            borderRadius: BorderRadius.circular(2.spx),
            child: Container(
              width: 60.spx,
              height: 50.spx,
              padding: EdgeInsets.zero,
              decoration: BoxDecoration(
                color: const Color(0xFF4d67ff),
                borderRadius: BorderRadius.circular(8.spx),
              ),
              child: Center(
                child: Text(
                  'Later',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10.spx,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: 4.spx),
          // 分享
          CustomSlidableAction(
            onPressed: (context) => _handleShare(context),
            backgroundColor: const Color(0xFF8760ff),
            padding: EdgeInsets.zero,
            borderRadius: BorderRadius.circular(2.spx),
            child: Container(
              width: 60.spx,
              height: 60.spx,
              margin: EdgeInsets.symmetric(horizontal: 4.spx),
              decoration: BoxDecoration(
                color: const Color(0xFF8760ff),
                borderRadius: BorderRadius.circular(8.spx),
              ),
              child: Center(
                child: Text(
                  'Share',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10.spx,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: 4.spx),
          // 收藏
          CustomSlidableAction(
            onPressed: (context) => widget.onBookmark(),
            backgroundColor: const Color(0xFFf2cb51),
            padding: EdgeInsets.zero,
            borderRadius: BorderRadius.circular(2.spx),
            child: Container(
              width: 60.spx,
              height: 50.spx,
              decoration: BoxDecoration(
                color: const Color(0xFFf2cb51),
                borderRadius: BorderRadius.circular(8.spx),
              ),
              child: Center(
                child: Text(
                  'Star',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10.spx,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 标题和描述 - 使用 Flexible 而不是 Expanded，以便在没有图片时能充满整行
          Flexible(
            flex: hasValidImage ? 1 : 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题
                Text(
                  widget.article['title'] ?? '',
                  style: Theme.of(context)
                      .textTheme
                      .titleMedium
                      ?.copyWith(fontSize: 13.spx, fontWeight: FontWeight.w600),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                // 描述
                if (_getArticleDescription().isNotEmpty) ...[
                  Text(
                    _getArticleDescription(),
                    style: TextStyle(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? const Color(0xFFD1D1D1)
                          : const Color(0xFF999999),
                      fontSize: 12.spx,
                      height: 1.4,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
          // 只有在有有效图片时才显示间距和图片
          if (hasValidImage) ...[
            SizedBox(width: 15.spx),
            // 文章封面图片
            _buildCoverImage(context),
          ],
        ],
      ),
    );
  }

  /// 构建第二行：源信息、日期和操作按钮
  Widget _buildSecondRow(BuildContext context) {
    return Row(
      children: [
        // 源Logo
        Container(
          width: 20.spx,
          height: 20.spx,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: Theme.of(context).primaryColor,
              width: 1,
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: widget.feed['img'] != null &&
                    widget.feed['img'].toString().isNotEmpty
                ? CachedNetworkImage(
                    imageUrl: widget.feed['img'],
                    fit: BoxFit.cover,
                    errorWidget: (context, error, stackTrace) {
                      return Icon(
                        Icons.rss_feed,
                        size: 12.spx,
                        color: Theme.of(context).primaryColor,
                      );
                    },
                    placeholder: (context, url) {
                      return Icon(
                        Icons.rss_feed,
                        size: 12.spx,
                        color: Theme.of(context).primaryColor,
                      );
                    },
                  )
                : Icon(
                    Icons.rss_feed,
                    size: 12.spx,
                    color: Theme.of(context).primaryColor,
                  ),
          ),
        ),
        const SizedBox(width: 8),
        // 源名称 - 使用 Expanded 占用所有可用空间，操作按钮自然被推到最右侧
        Expanded(
          child: Text(
            widget.feed['feedsName'] ?? 'SubFeeds',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? const Color(0xFFD1D1D1)
                      : const Color(0xFF999999),
                  fontSize: 12.spx,
                  fontWeight: FontWeight.w400,
                ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Text(
          formatTime(widget.article['pubDate']),
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFFD1D1D1)
                    : const Color(0xFF999999),
                fontSize: 12.spx,
                fontWeight: FontWeight.w400,
              ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  /// 构建封面图片
  Widget _buildCoverImage(BuildContext context) {
    // ✅ 关键：使用条件判断控制整个容器是否渲染
    if (widget.article['img'] == null || widget.article['img'].isEmpty) {
      return const SizedBox.shrink(); // 无图片时完全折叠
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(2.spx),
      child: CachedNetworkImage(
        imageUrl: widget.article['img'],
        width: 50.spx,
        height: 53.spx,
        fit: BoxFit.cover,
        errorWidget: (context, error, stackTrace) {
          // ✅ 关键：图片加载失败时设置状态并重新构建
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted && !_imageLoadFailed) {
              setState(() {
                _imageLoadFailed = true;
              });
            }
          });
          // 返回完全折叠的空部件
          return const SizedBox.shrink();
        },
        placeholder: (context, url) {
          // 保持加载状态占位符（仅在图片有效时显示）
          return Container(
            width: 50.spx,
            height: 50.spx,
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF2A2A2A)
                  : const Color(0xFFF5F5F5),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: LoadingIndicator(),
            ),
          );
        },
      ),
    );
  }

  /// 获取文章描述信息
  String _getArticleDescription() {
    // 尝试从不同字段获取描述信息
    String description = '';

    // 优先使用 description 字段
    if (widget.article['description'] != null &&
        widget.article['description'].toString().isNotEmpty) {
      description = widget.article['description'].toString();
    }
    // 其次使用 content 字段
    else if (widget.article['content'] != null &&
        widget.article['content'].toString().isNotEmpty) {
      description = widget.article['content'].toString();
    }
    // 最后使用 summary 字段
    else if (widget.article['summary'] != null &&
        widget.article['summary'].toString().isNotEmpty) {
      description = widget.article['summary'].toString();
    }

    // 如果有描述内容，则移除HTML标签并清理
    if (description.isNotEmpty) {
      description = stripHtmlTags(description);
      // 移除多余的空白字符和换行符
      description = description.replaceAll(RegExp(r'\s+'), ' ').trim();
    }

    return description;
  }

  /// 移除HTML标签，提取纯文本
  static String stripHtmlTags(String htmlText) {
    RegExp exp = RegExp(r"<[^>]*>", multiLine: true, caseSensitive: true);
    String strippedText = htmlText.replaceAll(exp, '');
    return strippedText.replaceAll('&nbsp;', ' ');
  }
}
